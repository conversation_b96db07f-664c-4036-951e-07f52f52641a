<!-- 引入筛选相关的 WXS 函数 -->
<wxs module="filterUtils" src="/utils/wxs/filter.wxs"></wxs>
<block wx:if="{{isRequest}}">
  <view class="page-container {{pageScrollDisabled ? 'scroll-disabled' : ''}}" style="{{pageContainerStyle}}">
    <common-header show_white="{{show_white}}" defaultBgColor="rgba(255,255,255)" id="commonHeader">
      <view slot="left" class="left">
        <image class="img-title" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_position_text.png"></image>
      </view>
      <view slot="right">
        <image class="search" bindtap="goSearch" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_search_black.png"></image>
      </view>
    </common-header>
    <view class="main-top {{showPopupFilterMenu || menuSticky ? 'bgf' : ''}}" style="--header-height: {{headerHeight}}px">
      <!-- 使用业务菜单组件 -->
      <business-menu id="announcement-business-menu" menu-list="{{menuList}}" selectData="{{jobSelectForTemplate}}" active-expanded="{{activeExpanded}}" tab-type="job" showPopupFilterMenu="{{showPopupFilterMenu}}" bind:menuClick="handleMenuClick" />

      <!-- 地区筛选列表 - 在business-menu组件平级位置 -->
      <view class="region-list-container" wx:if="{{showRegionList}}">
        <scroll-view scroll-x class="region-list-ul" show-scrollbar="{{false}}" enhanced>
          <block wx:if="{{isLogin}}">
            <view class="region-list-item {{filterUtils.isFilterItemOptionSelected(item.value, jobSelectForTemplate.apply_region) ? 'active' : ''}}" wx:for="{{jobMenuData.apply_region.data[0].list}}" wx:key="id" data-item="{{item}}" bindtap="handleRegionItemClick">
              {{item.name}}
            </view>
          </block>
          <block wx:else>
            <view class="region-list-item {{filterUtils.isFilterItemOptionSelected(item.value, jobSelectForTemplate.apply_region) ? 'active' : ''}}" wx:for="{{cachedRegions}}" wx:key="id" data-item="{{item}}" bindtap="handleRegionItemClick">
              {{item.name}}
            </view>
          </block>
        </scroll-view>
        <view class="right-box" bindtap="handleAddRegionClick">
          <image class="add-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_region_add.png"></image>
        </view>
      </view>
    </view>
    <view class="main-content {{jobData.complete_progress.is_tips==0?'pt120':''}}">
      <view class="position-list" wx:if="{{jobList.length>0}}">
        <view class="notes-box" bindtap="goResume" wx:if="{{isShowResume}}">
          <image class="notes-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_note_bg.png"></image>
          <van-circle style="height: 40px;" value="{{ jobData.complete_progress.progress }}" type="2d" layer-color="rgba(255, 106, 77, 0.15)" color="rgba(255, 106, 77, 1)" size="45">
            <view class="circle-text"><text class="num">{{jobData.complete_progress.progress}}</text>%</view>
          </van-circle>
          <view class="content-text">
            <view class="title">当前你的岗位匹配度较低</view>
            <view class="label">完善你的简历以提升岗位匹配度</view>
          </view>
          <view class="improve-btn">去完善</view>
          <view class="close-img-box" catchtap="closeNote">
            <image class="close-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_close_hui.png"></image>
          </view>
        </view>
        <job-list-card list="{{jobList}}"></job-list-card>
      </view>
      <empty-default imgurl="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/tabbar/focus_no_data.png" wx:if="{{jobList.length === 0}}" text="暂未找到适合你的职位~"></empty-default>
    </view>
  </view>
</block>

<!-- 菜单筛选弹窗 -->
<popup-menu-filter show="{{showPopupFilterMenu}}" popup-top="{{overlayTop}}" bind:close="handlePopupClose" custom-class="global-positioned">
  <!-- 菜单筛选内容作为 slot 传入 -->
  <menu-filter-content menu-type="{{activeExpanded}}" show-footer=" {{showFooter}}" animation-show="{{showPopupFilterMenu}}" bind:autoClose="handlePopupClose" bind:close="handlePopupClose">
    <block wx:if="{{activeExpanded == 'exam_type'}}">
      <exam-content show="{{showPopupFilterMenu}}" exam-list="{{jobMenuData.exam_type.data}}" isMultipleChoice="{{true}}" selected="{{jobSelectForTemplate.exam_type}}" filterKey="exam_type" bind:confirm="handleJobMenuFilterConfirm" />
    </block>
    <block wx:if="{{activeExpanded == 'filter_list'}}">
      <view class="popu-content">
        <view class="popu-content-c">
          <group-list show="{{showPopupFilterMenu}}" dataList="{{jobMenuData.filter_list.data}}" selected="{{jobSelectForTemplate.filter_list}}" filterKey="filter_list" bind:confirm="handleJobMenuFilterConfirm" />
        </view>
      </view>
    </block>
  </menu-filter-content>
</popup-menu-filter>

<!-- 底部导航栏 -->
<home-tabbar id="homeTabbar" active="job">
</home-tabbar>